name: Staging Deploy Pigeon Squad

on:
  push:
    branches:
      - main  # Change to your main branch

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v2
        
      - name: Add SSH private key to agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.SSH_PRIVATE_KEY }}

      - name: Deploy to Server
        env:
          SERVER_USER: ${{ secrets.SERVER_USER }}
          SERVER_IP: ${{ secrets.SERVER_IP }}
          APP_DIR: ${{ secrets.APP_DIR }}
        run: |
          ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SERVER_USER@$SERVER_IP "
          # Create app directory if it doesn't exist
          mkdir -p $APP_DIR &&
          cd $APP_DIR &&

          # Initialize git repository if it doesn't exist
          if [ ! -d .git ]; then
            echo 'Initializing git repository...'
            git init &&
            git remote <NAME_EMAIL>:kerv/sprout.git
          else
            echo 'Git repository exists, updating remote...'
            git remote set-<NAME_EMAIL>:kerv/sprout.git
          fi &&

          # Pull latest code
          git fetch origin main &&
          git reset --hard origin/main &&

          # Build new images first (while old services are still running)
          echo 'Building new Docker images...' &&
          docker compose build --no-cache &&

          # Now quickly stop old services and start new ones
          echo 'Restarting services with new images...' &&
          docker compose down &&
          docker compose up -d &&

          # Show container status
          docker compose ps &&
          echo 'Deployment completed! Services running!'