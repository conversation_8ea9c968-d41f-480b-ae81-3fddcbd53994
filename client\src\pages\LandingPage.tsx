import React from 'react';
import { Typography, Grid } from '@mui/material';
import { useTranslation } from 'react-i18next';
import { LandingPageProps } from '../types';
import NavBar from '../components/NavBar';
import {
  PageContainer,
  Section,
  ContentContainer,
  HeroSection,
  CardGrid,
  CenteredContent,
  PrimaryButton,
  SecondaryButton,
  FeatureCard,
  FeatureCardContent,
  FeatureIcon,
  HeroTitle,
  HeroSubtitle,
  SectionTitle,
  FeatureTitle,
  FeatureDescription,
  StatsContainer,
  StatNumber,
  StatLabel,
  LogoContainer,
  ActionButtonGroup,
} from '../components/styled';

const LandingPage: React.FC<LandingPageProps> = ({ onShowLogin }) => {
  const { t } = useTranslation();
  
  const scrollToFeatures = (e: React.MouseEvent<HTMLButtonElement>): void => {
    e.preventDefault();
    e.stopPropagation();
    const featuresSection = document.getElementById('features-section');
    if (featuresSection) {
      featuresSection.scrollIntoView({ 
        behavior: 'smooth',
        block: 'start'
      });
    }
  };

  const handleNavigation = (section: string) => {
    const element = document.getElementById(`${section}-section`);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' });
    }
  };

  const handleProfileClick = () => {
    onShowLogin();
  };

  return (
    <PageContainer>
      <NavBar onNavigate={handleNavigation} onProfileClick={handleProfileClick} />
      <HeroSection id="home-section" minHeight="100vh" background="gradient" sx={{ paddingTop: '80px' }}>
        <ContentContainer maxWidth="md">
          <CenteredContent maxWidth={800} padding={8}>
            <LogoContainer>
              <img src="/Pigeon Squad Logo.png" alt="Pigeon Squad" />
            </LogoContainer>
            <HeroTitle variant="h4">{t('app.title')}</HeroTitle>
            <Typography variant="h6" color="text.secondary" gutterBottom>{t('landing.subtitle')}</Typography>

            <HeroTitle variant="h4" sx={{ mt: 4 }}>
              {t('landing.headline')}
            </HeroTitle>
            <HeroSubtitle variant="body1">
              {t('landing.description')}
            </HeroSubtitle>

            <ActionButtonGroup>
              <PrimaryButton size="large" onClick={onShowLogin}>
                {t('landing.getStarted')}
              </PrimaryButton>
              <SecondaryButton variant="outlined" size="large" onClick={scrollToFeatures}>
                {t('landing.learnMore')}
              </SecondaryButton>
            </ActionButtonGroup>
          </CenteredContent>
        </ContentContainer>
      </HeroSection>

      <Section id="features-section" spacing="large" background="paper">
        <ContentContainer maxWidth="lg">
          <SectionTitle variant="h4">
            {t('landing.howWeHelp')}
          </SectionTitle>
          <CardGrid container spacing={4}>
            {[
              { icon: '📧', titleKey: 'landing.features.smartEmail.title', descKey: 'landing.features.smartEmail.desc' },
              { icon: '📅', titleKey: 'landing.features.autoReminders.title', descKey: 'landing.features.autoReminders.desc' },
              { icon: '👨👩👧👦', titleKey: 'landing.features.familySharing.title', descKey: 'landing.features.familySharing.desc' },
              { icon: '🎯', titleKey: 'landing.features.prioritySorting.title', descKey: 'landing.features.prioritySorting.desc' },
              { icon: '📱', titleKey: 'landing.features.multiPlatform.title', descKey: 'landing.features.multiPlatform.desc' },
              { icon: '🧠', titleKey: 'landing.features.learningAI.title', descKey: 'landing.features.learningAI.desc' }
            ].map((feature, index) => (
              <Grid size={{ xs: 12, md: 6, lg: 4 }} key={index}>
                <FeatureCard interactive>
                  <FeatureCardContent>
                    <FeatureIcon>{feature.icon}</FeatureIcon>
                    <FeatureTitle variant="h6">{t(feature.titleKey)}</FeatureTitle>
                    <FeatureDescription variant="body2">{t(feature.descKey)}</FeatureDescription>
                  </FeatureCardContent>
                </FeatureCard>
              </Grid>
            ))}
          </CardGrid>
        </ContentContainer>
      </Section>

      <StatsContainer>
        <ContentContainer maxWidth="lg">
          <Grid container spacing={4}>
            {[
              { number: '98%', labelKey: 'landing.stats.accuracy' },
              { number: '2.5k+', labelKey: 'landing.stats.families' },
              { number: '15min', labelKey: 'landing.stats.timeSaved' },
              { number: 'Zero', labelKey: 'landing.stats.missedEvents' }
            ].map((stat, index) => (
              <Grid size={{ xs: 6, md: 3 }} key={index}>
                <StatNumber variant="h3">
                  {stat.number}
                </StatNumber>
                <StatLabel variant="body1">{t(stat.labelKey)}</StatLabel>
              </Grid>
            ))}
          </Grid>
        </ContentContainer>
      </StatsContainer>

      {/* About Section */}
      <Section id="about-section" spacing="large" background="paper">
        <ContentContainer maxWidth="lg">
          <SectionTitle variant="h4">
            {t('nav.about')}
          </SectionTitle>
          <Typography variant="body1" sx={{ textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
            Learn more about our mission to help families stay organized and never miss important events.
          </Typography>
        </ContentContainer>
      </Section>

      {/* Skills Section */}
      <Section id="skills-section" spacing="large">
        <ContentContainer maxWidth="lg">
          <SectionTitle variant="h4">
            {t('nav.skills')}
          </SectionTitle>
          <Typography variant="body1" sx={{ textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
            Our AI-powered platform combines advanced email processing, natural language understanding, and smart scheduling.
          </Typography>
        </ContentContainer>
      </Section>

      {/* Projects Section */}
      <Section id="projects-section" spacing="large" background="paper">
        <ContentContainer maxWidth="lg">
          <SectionTitle variant="h4">
            {t('nav.projects')}
          </SectionTitle>
          <Typography variant="body1" sx={{ textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
            Discover how we've helped thousands of families stay organized and connected.
          </Typography>
        </ContentContainer>
      </Section>

      {/* Blogs Section */}
      <Section id="blogs-section" spacing="large">
        <ContentContainer maxWidth="lg">
          <SectionTitle variant="h4">
            {t('nav.blogs')}
          </SectionTitle>
          <Typography variant="body1" sx={{ textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
            Read our latest insights on family organization, parenting tips, and technology updates.
          </Typography>
        </ContentContainer>
      </Section>

      {/* Contact Section */}
      <Section id="contact-section" spacing="large" background="paper">
        <ContentContainer maxWidth="lg">
          <SectionTitle variant="h4">
            {t('nav.contact')}
          </SectionTitle>
          <Typography variant="body1" sx={{ textAlign: 'center', maxWidth: 800, mx: 'auto' }}>
            Get in touch with our team. We'd love to hear from you and help with any questions.
          </Typography>
        </ContentContainer>
      </Section>
    </PageContainer>
  );
};

export default LandingPage;